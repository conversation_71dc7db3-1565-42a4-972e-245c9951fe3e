' FormLogin.vb - Compatible dengan Designer
Imports MySql.Data.MySqlClient
Imports System.Security.Cryptography
Imports System.Text
Imports System.IO
Imports System.Drawing.Drawing2D

Public Class FormLogin
    Private Sub FormLogin_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' Setup form properties
        Me.Size = New Size(470, 600)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.FormBorderStyle = FormBorderStyle.None
        Me.BackColor = Color.White
        Me.Location = New Point(600, 200)

        ' Create UI controls
        SetupLoginUI()
    End Sub

    Private Sub SetupLoginUI()
        ' Clear existing controls first
        Me.Controls.Clear()

        ' Panel Header Utama
        Dim panelHeaderUtama As New Panel With {
            .Dock = DockStyle.Top,
            .Height = 140,
            .BackColor = Color.FromArgb(52, 73, 94)
        }

        ' Container untuk judul dan ikon
        Dim containerJudulDanIkon As New Panel With {
            .Size = New Size(350, 60),
            .Location = New Point(60, 15),
            .BackColor = Color.Transparent
        }

        ' Ikon Logo Toko
        Dim ikonLogoToko As New Label With {
            .Text = "🛒",
            .Font = New Font("Segoe UI", 26, FontStyle.Bold),
            .ForeColor = Color.White,
            .Size = New Size(45, 45),
            .Location = New Point(35, 20),
            .TextAlign = ContentAlignment.MiddleCenter
        }

        ' Judul Aplikasi
        Dim judulAplikasi As New Label With {
            .Text = "TOKO ONLINE",
            .Font = New Font("Segoe UI", 22, FontStyle.Bold),
            .ForeColor = Color.White,
            .AutoSize = True,
            .Location = New Point(80, 20),
            .TextAlign = ContentAlignment.MiddleCenter
        }

        containerJudulDanIkon.Controls.AddRange({ikonLogoToko, judulAplikasi})
        ' Pesan Selamat Datang
        Dim pesanSelamatDatang As New Label With {
            .Text = "Selamat datang! Silakan login untuk melanjutkan",
            .Font = New Font("Segoe UI", 9),
            .ForeColor = Color.FromArgb(189, 195, 199),
            .AutoSize = True,
            .Location = New Point(95, 90),
            .TextAlign = ContentAlignment.MiddleCenter,
            .BackColor = Color.Transparent
        }

        panelHeaderUtama.Controls.AddRange({containerJudulDanIkon, pesanSelamatDatang})

        ' Tombol Tutup Aplikasi
        Dim tombolTutupAplikasi As New Label With {
            .Text = "✕",
            .Font = New Font("Segoe UI", 14, FontStyle.Bold),
            .ForeColor = Color.White,
            .Size = New Size(35, 35),
            .Location = New Point(420, 10),
            .TextAlign = ContentAlignment.MiddleCenter,
            .Cursor = Cursors.Hand
        }
        AddHandler tombolTutupAplikasi.Click, Sub() Application.Exit()
        AddHandler tombolTutupAplikasi.MouseEnter, Sub()
                                                       tombolTutupAplikasi.BackColor = Color.FromArgb(231, 76, 60)
                                                       tombolTutupAplikasi.Font = New Font("Segoe UI", 16, FontStyle.Bold)
                                                   End Sub
        AddHandler tombolTutupAplikasi.MouseLeave, Sub()
                                                       tombolTutupAplikasi.BackColor = Color.Transparent
                                                       tombolTutupAplikasi.Font = New Font("Segoe UI", 14, FontStyle.Bold)
                                                   End Sub
        panelHeaderUtama.Controls.Add(tombolTutupAplikasi)

        ' Panel Formulir Login - Gaya Kartu
        Dim panelFormulirLogin As New Panel With {
            .Size = New Size(350, 350),
            .Location = New Point(50, 170),
            .BackColor = Color.White
        }

        ' Warna border untuk efek kartu
        Dim warnaBorderKartu As New Pen(Color.FromArgb(189, 195, 199), 1)

        ' Event handler untuk menggambar border
        AddHandler panelFormulirLogin.Paint, Sub(pengirimEvent As Object, eventGambar As PaintEventArgs)
                                                 Dim grafik As Graphics = eventGambar.Graphics
                                                 grafik.DrawRectangle(warnaBorderKartu, 0, 0, panelFormulirLogin.Width - 1, panelFormulirLogin.Height - 1)
                                             End Sub

        ' Judul Panel Login
        Dim judulPanelLogin As New Label With {
            .Text = "LOGIN",
            .Font = New Font("Segoe UI", 18, FontStyle.Bold),
            .ForeColor = Color.FromArgb(52, 73, 94),
            .Location = New Point(25, 15),
            .AutoSize = True,
            .BackColor = Color.Transparent
        }

        ' Label Username
        Dim labelUsername As New Label With {
            .Text = "👤 Username",
            .Font = New Font("Segoe UI", 11, FontStyle.Bold),
            .ForeColor = Color.FromArgb(52, 73, 94),
            .Location = New Point(25, 60),
            .AutoSize = True,
            .BackColor = Color.Transparent
        }

        Dim inputUsername As New TextBox With {
            .Name = "inputUsername",
            .Size = New Size(300, 40),
            .Location = New Point(25, 85),
            .Font = New Font("Segoe UI", 12),
            .BorderStyle = BorderStyle.FixedSingle,
            .BackColor = Color.FromArgb(250, 250, 250)
        }
        ' Efek fokus untuk input username
        AddHandler inputUsername.GotFocus, Sub()
                                               inputUsername.BackColor = Color.White
                                               inputUsername.ForeColor = Color.FromArgb(52, 73, 94)
                                           End Sub
        AddHandler inputUsername.LostFocus, Sub()
                                                inputUsername.BackColor = Color.FromArgb(250, 250, 250)
                                                inputUsername.ForeColor = Color.FromArgb(127, 140, 141)
                                            End Sub

        ' Label Password
        Dim labelPassword As New Label With {
            .Text = "🔒 Password",
            .Font = New Font("Segoe UI", 11, FontStyle.Bold),
            .ForeColor = Color.FromArgb(52, 73, 94),
            .Location = New Point(25, 135),
            .AutoSize = True,
            .BackColor = Color.Transparent
        }

        Dim inputPassword As New TextBox With {
            .Name = "inputPassword",
            .Size = New Size(300, 40),
            .Location = New Point(25, 160),
            .Font = New Font("Segoe UI", 12),
            .BorderStyle = BorderStyle.FixedSingle,
            .UseSystemPasswordChar = True,
            .BackColor = Color.FromArgb(250, 250, 250)
        }
        ' Efek fokus untuk input password
        AddHandler inputPassword.GotFocus, Sub()
                                               inputPassword.BackColor = Color.White
                                               inputPassword.ForeColor = Color.FromArgb(52, 73, 94)
                                           End Sub
        AddHandler inputPassword.LostFocus, Sub()
                                                inputPassword.BackColor = Color.FromArgb(250, 250, 250)
                                                inputPassword.ForeColor = Color.FromArgb(127, 140, 141)
                                            End Sub

        ' Checkbox Tampilkan Password
        Dim checkboxTampilkanPassword As New CheckBox With {
            .Text = "Tampilkan password",
            .Location = New Point(25, 210),
            .AutoSize = True,
            .Font = New Font("Segoe UI", 9),
            .ForeColor = Color.FromArgb(127, 140, 141),
            .BackColor = Color.Transparent
        }
        AddHandler checkboxTampilkanPassword.CheckedChanged, Sub()
                                                                 inputPassword.UseSystemPasswordChar = Not checkboxTampilkanPassword.Checked
                                                             End Sub

        ' Tombol Login
        Dim tombolLogin As New Button With {
            .Text = "LOGIN",
            .Size = New Size(300, 45),
            .Location = New Point(25, 240),
            .BackColor = Color.FromArgb(46, 204, 113),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 12, FontStyle.Bold),
            .Cursor = Cursors.Hand,
            .TextAlign = ContentAlignment.MiddleCenter,
            .ImageAlign = ContentAlignment.MiddleCenter,
            .TextImageRelation = TextImageRelation.ImageBeforeText
        }

        ' Tambahkan gambar login dengan warna
        Dim loginImagePath As String = Path.Combine(Application.StartupPath, "..", "..", "Assets", "Images", "icons", "login.png")
        If File.Exists(loginImagePath) Then
            Dim img As New Bitmap(loginImagePath)
            Dim coloredImg As Bitmap = BuatIkonBerwarna(img, Color.Yellow, New Size(20, 20))
            tombolLogin.Image = coloredImg
            img.Dispose()
        End If
        tombolLogin.FlatAppearance.BorderSize = 0
        tombolLogin.FlatAppearance.MouseOverBackColor = Color.FromArgb(39, 174, 96)
        AddHandler tombolLogin.Click, AddressOf ProsesLogin

        ' Efek hover untuk tombol login
        AddHandler tombolLogin.MouseEnter, Sub()
                                               tombolLogin.Font = New Font("Segoe UI", 13, FontStyle.Bold)
                                           End Sub
        AddHandler tombolLogin.MouseLeave, Sub()
                                               tombolLogin.Font = New Font("Segoe UI", 12, FontStyle.Bold)
                                           End Sub

        ' Tombol Daftar
        Dim tombolDaftar As New Button With {
            .Text = "DAFTAR AKUN BARU",
            .Size = New Size(300, 40),
            .Location = New Point(25, 300),
            .BackColor = Color.FromArgb(52, 152, 219),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Cursor = Cursors.Hand,
            .TextAlign = ContentAlignment.MiddleCenter,
            .ImageAlign = ContentAlignment.MiddleCenter,
            .TextImageRelation = TextImageRelation.ImageBeforeText
        }

        ' Tambahkan gambar register dengan warna
        Dim registerImagePath As String = Path.Combine(Application.StartupPath, "..", "..", "Assets", "Images", "icons", "register.png")
        If File.Exists(registerImagePath) Then
            Dim img As New Bitmap(registerImagePath)
            Dim coloredImg As Bitmap = BuatIkonBerwarna(img, Color.Orange, New Size(20, 20))
            tombolDaftar.Image = coloredImg
            img.Dispose()
        End If
        tombolDaftar.FlatAppearance.BorderSize = 0
        tombolDaftar.FlatAppearance.MouseOverBackColor = Color.FromArgb(41, 128, 185)
        AddHandler tombolDaftar.Click, AddressOf BukaDaftarAkun

        ' Efek hover untuk tombol daftar
        AddHandler tombolDaftar.MouseEnter, Sub()
                                                tombolDaftar.Font = New Font("Segoe UI", 11, FontStyle.Bold)
                                            End Sub
        AddHandler tombolDaftar.MouseLeave, Sub()
                                                tombolDaftar.Font = New Font("Segoe UI", 10, FontStyle.Bold)
                                            End Sub

        panelFormulirLogin.Controls.AddRange({judulPanelLogin, labelUsername, inputUsername, labelPassword, inputPassword, checkboxTampilkanPassword, tombolLogin, tombolDaftar})

        Me.Controls.AddRange({panelHeaderUtama, panelFormulirLogin})
    End Sub

    Private Sub ProsesLogin(pengirimEvent As Object, eventArgs As EventArgs)
        Dim inputUsername As TextBox = CType(Me.Controls.Find("inputUsername", True).FirstOrDefault(), TextBox)
        Dim inputPassword As TextBox = CType(Me.Controls.Find("inputPassword", True).FirstOrDefault(), TextBox)

        If inputUsername Is Nothing OrElse inputPassword Is Nothing Then
            MessageBox.Show("Error: Kontrol tidak ditemukan!", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return
        End If

        Dim namaUser As String = inputUsername.Text.Trim()
        Dim kataSandi As String = inputPassword.Text

        If namaUser = "" Or kataSandi = "" Then
            MessageBox.Show("Username dan password harus diisi!", "Peringatan", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Try
            koneksi()

            ' Query untuk login
            perintahSQL = New MySqlCommand("SELECT * FROM users WHERE username = @username AND password = @password", koneksiDatabase)
            perintahSQL.Parameters.AddWithValue("@username", namaUser)
            perintahSQL.Parameters.AddWithValue("@password", kataSandi)

            pembacaData = perintahSQL.ExecuteReader()

            If pembacaData.Read() Then
                ' Login berhasil
                Dim userID As Integer = Convert.ToInt32(pembacaData("user_id"))
                Dim fullName As String = pembacaData("full_name").ToString()

                ' Check if role column exists and get role
                Dim userRole As String = "user" ' Default role
                Dim isAdmin As Boolean = False

                Try
                    ' Try to get role from database
                    userRole = pembacaData("role").ToString()
                    isAdmin = (userRole.ToLower() = "admin")
                Catch ex As Exception
                    ' If role column doesn't exist, fallback to username check
                    Dim dbUsername As String = pembacaData("username").ToString()
                    isAdmin = (dbUsername.ToLower() = "admin")
                    userRole = If(isAdmin, "admin", "user")
                End Try

                pembacaData.Close()
                tutupKoneksi()

                MessageBox.Show($"Selamat datang, {fullName}!", "Login Berhasil", MessageBoxButtons.OK, MessageBoxIcon.Information)

                ' Redirect based on role
                If isAdmin Then
                    ' Admin langsung ke Admin Dashboard
                    Dim formAdminDashboard As New FormAdminDashboard()
                    formAdminDashboard.currentUserID = userID
                    formAdminDashboard.currentUserName = fullName
                    formAdminDashboard.Show()
                    Me.Hide()
                Else
                    ' User biasa ke FormMain
                    Dim formMain As New FormMain()
                    formMain.currentUserID = userID
                    formMain.currentUserName = fullName
                    formMain.isAdmin = isAdmin
                    formMain.Show()
                    Me.Hide()
                End If
            Else
                pembacaData.Close()
                tutupKoneksi()
                MessageBox.Show("Username atau password salah!", "Login Gagal", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End If

        Catch ex As Exception
            MessageBox.Show("Error: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            If pembacaData IsNot Nothing AndAlso Not pembacaData.IsClosed Then pembacaData.Close()
            tutupKoneksi()
        End Try
    End Sub

    Private Sub BukaDaftarAkun(pengirimEvent As Object, eventArgs As EventArgs)
        ' Buka form pendaftaran akun baru
        Dim formPendaftaran As New FormRegister()
        formPendaftaran.ShowDialog()
    End Sub

    Private Function GetMD5Hash(input As String) As String
        Using md5 As MD5 = MD5.Create()
            Dim inputBytes As Byte() = Encoding.ASCII.GetBytes(input)
            Dim hashBytes As Byte() = md5.ComputeHash(inputBytes)

            Dim sb As New StringBuilder()
            For Each b As Byte In hashBytes
                sb.Append(b.ToString("X2"))
            Next
            Return sb.ToString()
        End Using
    End Function

    Private Function BuatIkonBerwarna(originalImage As Bitmap, warna As Color, ukuran As Size) As Bitmap
        Dim coloredBitmap As New Bitmap(ukuran.Width, ukuran.Height)
        Using g As Graphics = Graphics.FromImage(coloredBitmap)
            g.Clear(Color.Transparent)

            ' Buat color matrix untuk mengubah warna
            Dim colorMatrix As New Imaging.ColorMatrix()
            colorMatrix.Matrix33 = 1.0F ' Alpha
            colorMatrix.Matrix00 = warna.R / 255.0F ' Red
            colorMatrix.Matrix11 = warna.G / 255.0F ' Green
            colorMatrix.Matrix22 = warna.B / 255.0F ' Blue

            Dim imageAttributes As New Imaging.ImageAttributes()
            imageAttributes.SetColorMatrix(colorMatrix)

            g.DrawImage(originalImage, New Rectangle(0, 0, ukuran.Width, ukuran.Height),
                       0, 0, originalImage.Width, originalImage.Height, GraphicsUnit.Pixel, imageAttributes)
        End Using
        Return coloredBitmap
    End Function
End Class