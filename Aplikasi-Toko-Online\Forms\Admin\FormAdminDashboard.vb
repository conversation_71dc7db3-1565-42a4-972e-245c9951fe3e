﻿' FormAdminDashboard.vb - Dashboard untuk Admin
Imports MySql.Data.MySqlClient

Public Class FormAdminDashboard
    ' Properties untuk menyimpan info admin
    Public Property currentUserID As Integer
    Public Property currentUserName As String

    Private Sub FormAdminDashboard_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Me.Text = "Admin Dashboard - Toko Online Management System"
        Me.WindowState = FormWindowState.Maximized
        Me.BackColor = Color.FromArgb(45, 52, 54) ' Dark theme

        CreateDashboardUI()
        LoadDashboardData()
    End Sub

    Private Sub CreateDashboardUI()
        ' Top Navigation Bar
        Dim pnlTopNav As New Panel With {
            .Dock = DockStyle.Top,
            .Height = 70,
            .BackColor = Color.FromArgb(33, 37, 41)
        }

        ' Logo and Title
        Dim lblTitle As New Label With {
            .Text = "ADMIN PANEL",
            .Font = New Font("Segoe UI", 18, FontStyle.Bold),
            .ForeColor = Color.White,
            .Location = New Point(20, 20),
            .AutoSize = True
        }

        ' Admin info panel
        Dim pnlAdminInfo As New Panel With {
            .Size = New Size(300, 50),
            .Location = New Point(Me.Width - 350, 10),
            .BackColor = Color.Transparent
        }

        Dim lblAdminIcon As New Label With {
            .Text = "👤",
            .Font = New Font("Segoe UI", 16),
            .ForeColor = Color.White,
            .Location = New Point(0, 15),
            .AutoSize = True
        }

        Dim lblAdminText As New Label With {
            .Text = "Administrator",
            .Font = New Font("Segoe UI", 12, FontStyle.Bold),
            .ForeColor = Color.White,
            .Location = New Point(40, 10),
            .AutoSize = True
        }

        Dim lblDateTime As New Label With {
            .Text = DateTime.Now.ToString("dd/MM/yyyy HH:mm"),
            .Font = New Font("Segoe UI", 10),
            .ForeColor = Color.FromArgb(173, 181, 189),
            .Location = New Point(30, 30),
            .AutoSize = True
        }

        pnlAdminInfo.Controls.AddRange({lblAdminIcon, lblAdminText, lblDateTime})
        pnlTopNav.Controls.AddRange({lblTitle, pnlAdminInfo})

        ' Side Navigation Panel
        Dim pnlSideNav As New Panel With {
            .Dock = DockStyle.Left,
            .Width = 250,
            .BackColor = Color.FromArgb(52, 58, 64)
        }

        ' Navigation Menu
        CreateNavigationMenu(pnlSideNav)

        ' Main Content Area
        Dim pnlMainContent As New Panel With {
            .Name = "pnlMainContent",
            .Dock = DockStyle.Fill,
            .BackColor = Color.FromArgb(248, 249, 250),
            .Padding = New Padding(20)
        }

        ' Stats Panel
        Dim pnlStats As New FlowLayoutPanel With {
            .Name = "pnlStats",
            .Dock = DockStyle.Top,
            .Height = 140,
            .BackColor = Color.Transparent,
            .Padding = New Padding(0, 0, 0, 20)
        }



        ' Recent Activity Panel
        Dim pnlRecentActivity As New Panel With {
            .Name = "pnlRecentActivity",
            .Dock = DockStyle.Fill,
            .BackColor = Color.White
        }

        pnlMainContent.Controls.AddRange({pnlRecentActivity, pnlStats})
        Me.Controls.AddRange({pnlMainContent, pnlSideNav, pnlTopNav})
    End Sub

    Private Sub CreateNavigationMenu(pnlSideNav As Panel)
        Dim yPos As Integer = 20

        ' Dashboard
        Dim btnDashboard As Button = CreateNavButton("📊 Dashboard", yPos)
        AddHandler btnDashboard.Click, Sub() ShowDashboard()
        yPos += 60

        ' Product Management
        Dim btnProducts As Button = CreateNavButton("📦 Kelola Produk", yPos)
        AddHandler btnProducts.Click, Sub() ShowProductManagement()
        yPos += 60

        ' Order Management
        Dim btnOrders As Button = CreateNavButton("🛒 Kelola Pesanan", yPos)
        AddHandler btnOrders.Click, Sub() ShowOrderManagement()
        yPos += 60

        ' User Management
        Dim btnUsers As Button = CreateNavButton("👥 Kelola Pengguna", yPos)
        AddHandler btnUsers.Click, Sub() ShowUserManagement()
        yPos += 60

        ' Reports
        Dim btnReports As Button = CreateNavButton("📈 Laporan", yPos)
        AddHandler btnReports.Click, Sub() ShowReports()
        yPos += 60

        ' Settings
        Dim btnSettings As Button = CreateNavButton("⚙️ Pengaturan", yPos)
        AddHandler btnSettings.Click, Sub() ShowSettings()
        yPos += 60

        ' Switch to User View
        Dim btnUserView As Button = CreateNavButton("👤 Lihat Sebagai User", yPos)
        btnUserView.BackColor = Color.FromArgb(52, 152, 219)
        AddHandler btnUserView.Click, Sub() SwitchToUserView()
        yPos += 80

        ' Logout
        Dim btnLogout As Button = CreateNavButton("🚪 Logout", yPos)
        btnLogout.BackColor = Color.FromArgb(220, 53, 69)
        AddHandler btnLogout.Click, Sub() LogoutAdmin()

        pnlSideNav.Controls.AddRange({btnDashboard, btnProducts, btnOrders, btnUsers, btnReports, btnSettings, btnLogout})
    End Sub

    Private Function CreateNavButton(text As String, yPos As Integer) As Button
        Dim btn As New Button With {
            .Text = text,
            .Size = New Size(230, 50),
            .Location = New Point(10, yPos),
            .FlatStyle = FlatStyle.Flat,
            .BackColor = Color.FromArgb(73, 80, 87),
            .ForeColor = Color.White,
            .Font = New Font("Segoe UI", 11, FontStyle.Bold),
            .TextAlign = ContentAlignment.MiddleLeft,
            .Padding = New Padding(15, 0, 0, 0),
            .Cursor = Cursors.Hand
        }
        btn.FlatAppearance.BorderSize = 0
        btn.FlatAppearance.MouseOverBackColor = Color.FromArgb(108, 117, 125)
        Return btn
    End Function

    Private Sub LoadDashboardData()
        Try
            koneksi()

            ' Load statistics
            Dim pnlStats As FlowLayoutPanel = CType(Me.Controls.Find("pnlStats", True).FirstOrDefault(), FlowLayoutPanel)
            If pnlStats IsNot Nothing Then
                pnlStats.Controls.Clear()

                ' Total Products
                perintahSQL = New MySqlCommand("SELECT COUNT(*) FROM products", koneksiDatabase)
                Dim totalProducts As Integer = Convert.ToInt32(perintahSQL.ExecuteScalar())
                pnlStats.Controls.Add(CreateModernStatCard("📦", "Total Produk", totalProducts.ToString(), Color.FromArgb(52, 152, 219)))

                ' Total Orders
                perintahSQL = New MySqlCommand("SELECT COUNT(*) FROM orders", koneksiDatabase)
                Dim totalOrders As Integer = Convert.ToInt32(perintahSQL.ExecuteScalar())
                pnlStats.Controls.Add(CreateModernStatCard("🛒", "Total Pesanan", totalOrders.ToString(), Color.FromArgb(46, 204, 113)))

                ' Total Users
                perintahSQL = New MySqlCommand("SELECT COUNT(*) FROM users", koneksiDatabase)
                Dim totalUsers As Integer = Convert.ToInt32(perintahSQL.ExecuteScalar())
                pnlStats.Controls.Add(CreateModernStatCard("👥", "Total Pengguna", totalUsers.ToString(), Color.FromArgb(155, 89, 182)))

                ' Total Revenue
                perintahSQL = New MySqlCommand("SELECT COALESCE(SUM(total_amount), 0) FROM orders WHERE order_status = 'completed'", koneksiDatabase)
                Dim totalRevenue As Decimal = Convert.ToDecimal(perintahSQL.ExecuteScalar())
                pnlStats.Controls.Add(CreateModernStatCard("💰", "Total Pendapatan", "Rp " & totalRevenue.ToString("N0"), Color.FromArgb(241, 196, 15)))
            End If



            ' Load recent activity
            LoadRecentActivity()

            tutupKoneksi()

        Catch ex As Exception
            MessageBox.Show("Error loading dashboard: " & ex.Message)
        End Try
    End Sub

    Private Function CreateModernStatCard(icon As String, title As String, value As String, color As Color) As Panel
        Dim card As New Panel With {
            .Size = New Size(280, 110),
            .BackColor = Color.White,
            .Margin = New Padding(10)
        }

        ' Add modern shadow and border
        AddHandler card.Paint, Sub(sender, e)
                                   ' Shadow
                                   Dim shadowRect As Rectangle = New Rectangle(3, 3, card.Width - 3, card.Height - 3)
                                   Using shadowBrush As New SolidBrush(Color.FromArgb(30, 0, 0, 0))
                                       e.Graphics.FillRectangle(shadowBrush, shadowRect)
                                   End Using

                                   ' Border
                                   Using borderPen As New Pen(Color.FromArgb(230, 230, 230), 1)
                                       e.Graphics.DrawRectangle(borderPen, 0, 0, card.Width - 1, card.Height - 1)
                                   End Using
                               End Sub

        ' Icon background
        Dim pnlIconBg As New Panel With {
            .Size = New Size(60, 60),
            .Location = New Point(15, 15),
            .BackColor = Color.FromArgb(30, color.R, color.G, color.B)
        }

        Dim lblIcon As New Label With {
            .Text = icon,
            .Font = New Font("Segoe UI", 24),
            .ForeColor = color,
            .Dock = DockStyle.Fill,
            .TextAlign = ContentAlignment.MiddleCenter
        }

        pnlIconBg.Controls.Add(lblIcon)

        Dim lblTitle As New Label With {
            .Text = title,
            .Font = New Font("Segoe UI", 11),
            .ForeColor = Color.FromArgb(108, 117, 125),
            .Location = New Point(90, 20),
            .AutoSize = True
        }

        Dim lblValue As New Label With {
            .Text = value,
            .Font = New Font("Segoe UI", 22, FontStyle.Bold),
            .ForeColor = Color.FromArgb(52, 58, 64),
            .Location = New Point(90, 45),
            .AutoSize = True
        }

        card.Controls.AddRange({pnlIconBg, lblTitle, lblValue})
        Return card
    End Function



    Private Sub LoadRecentActivity()
        Dim pnlRecentActivity As Panel = CType(Me.Controls.Find("pnlRecentActivity", True).FirstOrDefault(), Panel)
        If pnlRecentActivity IsNot Nothing Then
            pnlRecentActivity.Controls.Clear()

            Dim lblTitle As New Label With {
                .Text = "Aktivitas Terbaru",
                .Font = New Font("Segoe UI", 14, FontStyle.Bold),
                .ForeColor = Color.FromArgb(52, 58, 64),
                .Location = New Point(20, 20),
                .AutoSize = True
            }

            ' Create a simple list of recent activities
            Dim lstActivity As New ListBox With {
                .Size = New Size(pnlRecentActivity.Width - 40, pnlRecentActivity.Height - 60),
                .Location = New Point(20, 60),
                .Font = New Font("Segoe UI", 10),
                .BorderStyle = BorderStyle.None,
                .BackColor = Color.FromArgb(248, 249, 250)
            }

            ' Add sample activities (you can replace with real data)
            lstActivity.Items.Add("📦 Produk baru ditambahkan: Laptop Gaming")
            lstActivity.Items.Add("🛒 Pesanan baru diterima: #ORD001")
            lstActivity.Items.Add("👤 User baru mendaftar: <EMAIL>")
            lstActivity.Items.Add("💰 Pembayaran berhasil: Rp 2,500,000")
            lstActivity.Items.Add("📋 Stok produk diperbarui: Mouse Gaming")

            pnlRecentActivity.Controls.AddRange({lblTitle, lstActivity})
        End If
    End Sub

    Private Sub ShowDashboard()
        ' Refresh dashboard data
        LoadDashboardData()
    End Sub

    Private Sub ShowProductManagement()
        Dim formProduct As New FormAdminProducts()
        formProduct.ShowDialog()
        ' Refresh data after closing product management
        LoadDashboardData()
    End Sub

    Private Sub ShowOrderManagement()
        Dim formOrders As New FormAdminOrders()
        formOrders.ShowDialog()
        ' Refresh data after closing order management
        LoadDashboardData()
    End Sub

    Private Sub ShowUserManagement()
        Dim formUsers As New FormAdminUsers()
        formUsers.ShowDialog()
        ' Refresh data after closing user management
        LoadDashboardData()
    End Sub

    Private Sub ShowReports()
        MessageBox.Show("Fitur Reports akan segera tersedia", "Info", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub ShowSettings()
        MessageBox.Show("Fitur Settings akan segera tersedia", "Info", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub SwitchToUserView()
        If MessageBox.Show("Beralih ke tampilan user?", "Konfirmasi", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            Dim formMain As New FormMain()
            formMain.currentUserID = Me.currentUserID
            formMain.currentUserName = Me.currentUserName
            formMain.isAdmin = True ' Tetap admin tapi lihat sebagai user
            formMain.Show()
            Me.Hide()
        End If
    End Sub

    Private Sub LogoutAdmin()
        If MessageBox.Show("Yakin ingin logout dari Admin Panel?", "Konfirmasi Logout", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            Dim formLogin As New FormLogin()
            formLogin.Show()
            Me.Close()
        End If
    End Sub
End Class