' FormAdminProducts.vb - Form khusus admin untuk pengelolaan produk
Imports MySql.Data.MySqlClient

Public Class FormAdminProducts
    Private Sub FormAdminProducts_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Me.Text = "Admin - Kelola Produk"
        Me.Size = New Size(1200, 700)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.BackColor = Color.FromArgb(248, 249, 250)

        CreateUI()
        LoadCategories()
        LoadProducts()
    End Sub

    Private Sub CreateUI()
        ' Header Panel
        Dim pnlHeader As New Panel With {
            .Dock = DockStyle.Top,
            .Height = 80,
            .BackColor = Color.FromArgb(52, 58, 64)
        }

        Dim lblTitle As New Label With {
            .Text = "KELOLA PRODUK",
            .Font = New Font("Segoe UI", 20, FontStyle.Bold),
            .ForeColor = Color.White,
            .Location = New Point(20, 25),
            .AutoSize = True
        }

        Dim btnClose As New Button With {
            .Text = "✕",
            .Size = New Size(45, 45),
            .Location = New Point(1140, 17),
            .FlatStyle = FlatStyle.Flat,
            .BackColor = Color.FromArgb(231, 76, 60),
            .ForeColor = Color.White,
            .Font = New Font("Segoe UI", 18, FontStyle.Bold),
            .Cursor = Cursors.Hand,
            .TextAlign = ContentAlignment.MiddleCenter,
            .Anchor = AnchorStyles.Top Or AnchorStyles.Right
        }
        btnClose.FlatAppearance.BorderSize = 0
        AddHandler btnClose.Click, Sub() Me.Close()

        ' Hover effects untuk close button
        AddHandler btnClose.MouseEnter, Sub()
                                            btnClose.BackColor = Color.FromArgb(192, 57, 43)
                                        End Sub
        AddHandler btnClose.MouseLeave, Sub()
                                            btnClose.BackColor = Color.FromArgb(231, 76, 60)
                                        End Sub

        pnlHeader.Controls.AddRange({lblTitle, btnClose})

        ' Toolbar Panel dengan design yang lebih rapi
        Dim pnlToolbar As New Panel With {
            .Dock = DockStyle.Top,
            .Height = 70,
            .BackColor = Color.FromArgb(248, 249, 250),
            .Padding = New Padding(20, 15, 20, 15)
        }

        ' Add subtle border bottom
        AddHandler pnlToolbar.Paint, Sub(sender, e)
                                          Dim pen As New Pen(Color.FromArgb(220, 220, 220), 1)
                                          e.Graphics.DrawLine(pen, 0, pnlToolbar.Height - 1, pnlToolbar.Width, pnlToolbar.Height - 1)
                                          pen.Dispose()
                                      End Sub

        ' Search controls dengan styling yang lebih rapi
        Dim lblSearch As New Label With {
            .Text = "Cari Produk:",
            .Font = New Font("Segoe UI", 11, FontStyle.Bold),
            .Location = New Point(20, 20),
            .AutoSize = True,
            .ForeColor = Color.FromArgb(52, 73, 94)
        }

        Dim txtSearch As New TextBox With {
            .Name = "txtSearch",
            .Size = New Size(180, 30),
            .Location = New Point(120, 17),
            .Font = New Font("Segoe UI", 11),
            .BorderStyle = BorderStyle.FixedSingle,
            .BackColor = Color.White,
            .ForeColor = Color.FromArgb(52, 73, 94)
        }
        ' Add placeholder effect
        txtSearch.Text = "Ketik nama produk..."
        txtSearch.ForeColor = Color.Gray
        AddHandler txtSearch.Enter, Sub()
                                        If txtSearch.Text = "Ketik nama produk..." Then
                                            txtSearch.Text = ""
                                            txtSearch.ForeColor = Color.FromArgb(52, 73, 94)
                                        End If
                                    End Sub
        AddHandler txtSearch.Leave, Sub()
                                        If txtSearch.Text = "" Then
                                            txtSearch.Text = "Ketik nama produk..."
                                            txtSearch.ForeColor = Color.Gray
                                        End If
                                    End Sub
        AddHandler txtSearch.TextChanged, AddressOf TxtSearch_TextChanged

        ' Category filter
        Dim lblCategory As New Label With {
            .Text = "Filter Kategori:",
            .Font = New Font("Segoe UI", 11, FontStyle.Bold),
            .Location = New Point(320, 20),
            .AutoSize = True,
            .ForeColor = Color.FromArgb(52, 73, 94)
        }

        Dim cmbCategory As New ComboBox With {
            .Name = "cmbCategory",
            .Size = New Size(160, 30),
            .Location = New Point(440, 17),
            .DropDownStyle = ComboBoxStyle.DropDownList,
            .Font = New Font("Segoe UI", 11),
            .BackColor = Color.White,
            .ForeColor = Color.FromArgb(52, 73, 94)
        }
        AddHandler cmbCategory.SelectedIndexChanged, AddressOf CmbCategory_SelectedIndexChanged

        ' Action buttons dengan design yang rapi
        Dim btnAdd As New Button With {
            .Text = "TAMBAH",
            .Size = New Size(120, 40),
            .Location = New Point(620, 15),
            .FlatStyle = FlatStyle.Flat,
            .BackColor = Color.FromArgb(46, 204, 113),
            .ForeColor = Color.White,
            .Font = New Font("Segoe UI", 11, FontStyle.Bold),
            .Cursor = Cursors.Hand,
            .TextAlign = ContentAlignment.MiddleCenter
        }
        btnAdd.FlatAppearance.BorderSize = 0
        btnAdd.FlatAppearance.BorderColor = Color.FromArgb(39, 174, 96)
        AddHandler btnAdd.Click, AddressOf BtnAdd_Click

        ' Hover effects untuk btnAdd
        AddHandler btnAdd.MouseEnter, Sub()
                                          btnAdd.BackColor = Color.FromArgb(39, 174, 96)
                                      End Sub
        AddHandler btnAdd.MouseLeave, Sub()
                                          btnAdd.BackColor = Color.FromArgb(46, 204, 113)
                                      End Sub

        Dim btnEdit As New Button With {
            .Text = "EDIT",
            .Size = New Size(100, 40),
            .Location = New Point(750, 15),
            .FlatStyle = FlatStyle.Flat,
            .BackColor = Color.FromArgb(241, 196, 15),
            .ForeColor = Color.White,
            .Font = New Font("Segoe UI", 11, FontStyle.Bold),
            .Cursor = Cursors.Hand,
            .TextAlign = ContentAlignment.MiddleCenter
        }
        btnEdit.FlatAppearance.BorderSize = 0
        btnEdit.FlatAppearance.BorderColor = Color.FromArgb(243, 156, 18)
        AddHandler btnEdit.Click, AddressOf BtnEdit_Click

        ' Hover effects untuk btnEdit
        AddHandler btnEdit.MouseEnter, Sub()
                                           btnEdit.BackColor = Color.FromArgb(243, 156, 18)
                                       End Sub
        AddHandler btnEdit.MouseLeave, Sub()
                                           btnEdit.BackColor = Color.FromArgb(241, 196, 15)
                                       End Sub

        Dim btnDelete As New Button With {
            .Text = "HAPUS",
            .Size = New Size(100, 40),
            .Location = New Point(860, 15),
            .FlatStyle = FlatStyle.Flat,
            .BackColor = Color.FromArgb(231, 76, 60),
            .ForeColor = Color.White,
            .Font = New Font("Segoe UI", 11, FontStyle.Bold),
            .Cursor = Cursors.Hand,
            .TextAlign = ContentAlignment.MiddleCenter
        }
        btnDelete.FlatAppearance.BorderSize = 0
        btnDelete.FlatAppearance.BorderColor = Color.FromArgb(192, 57, 43)
        AddHandler btnDelete.Click, AddressOf BtnDelete_Click

        ' Hover effects untuk btnDelete
        AddHandler btnDelete.MouseEnter, Sub()
                                             btnDelete.BackColor = Color.FromArgb(192, 57, 43)
                                         End Sub
        AddHandler btnDelete.MouseLeave, Sub()
                                             btnDelete.BackColor = Color.FromArgb(231, 76, 60)
                                         End Sub

        Dim btnRefresh As New Button With {
            .Text = "REFRESH",
            .Size = New Size(110, 40),
            .Location = New Point(970, 15),
            .FlatStyle = FlatStyle.Flat,
            .BackColor = Color.FromArgb(52, 152, 219),
            .ForeColor = Color.White,
            .Font = New Font("Segoe UI", 11, FontStyle.Bold),
            .Cursor = Cursors.Hand,
            .TextAlign = ContentAlignment.MiddleCenter
        }
        btnRefresh.FlatAppearance.BorderSize = 0
        btnRefresh.FlatAppearance.BorderColor = Color.FromArgb(41, 128, 185)
        AddHandler btnRefresh.Click, Sub() LoadProducts()

        ' Hover effects untuk btnRefresh
        AddHandler btnRefresh.MouseEnter, Sub()
                                              btnRefresh.BackColor = Color.FromArgb(41, 128, 185)
                                          End Sub
        AddHandler btnRefresh.MouseLeave, Sub()
                                              btnRefresh.BackColor = Color.FromArgb(52, 152, 219)
                                          End Sub

        pnlToolbar.Controls.AddRange({lblSearch, txtSearch, lblCategory, cmbCategory, btnAdd, btnEdit, btnDelete, btnRefresh})

        ' Main Panel for DataGridView
        Dim pnlMain As New Panel With {
            .Dock = DockStyle.Fill,
            .Padding = New Padding(20)
        }

        ' DataGridView
        Dim dgvProducts As New DataGridView With {
            .Name = "dgvProducts",
            .Dock = DockStyle.Fill,
            .AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
            .AllowUserToAddRows = False,
            .AllowUserToDeleteRows = False,
            .SelectionMode = DataGridViewSelectionMode.FullRowSelect,
            .ReadOnly = True,
            .BackgroundColor = Color.White,
            .BorderStyle = BorderStyle.None,
            .RowHeadersVisible = False,
            .Font = New Font("Segoe UI", 10),
            .RowTemplate = New DataGridViewRow With {.Height = 40}
        }

        ' Style the DataGridView
        dgvProducts.DefaultCellStyle.SelectionBackColor = Color.FromArgb(52, 152, 219)
        dgvProducts.DefaultCellStyle.SelectionForeColor = Color.White
        dgvProducts.DefaultCellStyle.Padding = New Padding(5)
        dgvProducts.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 58, 64)
        dgvProducts.ColumnHeadersDefaultCellStyle.ForeColor = Color.White
        dgvProducts.ColumnHeadersDefaultCellStyle.Font = New Font("Segoe UI", 11, FontStyle.Bold)
        dgvProducts.ColumnHeadersHeight = 45
        dgvProducts.EnableHeadersVisualStyles = False

        AddHandler dgvProducts.CellDoubleClick, AddressOf DgvProducts_CellDoubleClick

        pnlMain.Controls.Add(dgvProducts)

        ' Status Panel
        Dim pnlStatus As New Panel With {
            .Dock = DockStyle.Bottom,
            .Height = 30,
            .BackColor = Color.FromArgb(248, 249, 250)
        }

        Dim lblStatus As New Label With {
            .Name = "lblStatus",
            .Text = "Siap",
            .Font = New Font("Segoe UI", 9),
            .ForeColor = Color.FromArgb(108, 117, 125),
            .Location = New Point(20, 8),
            .AutoSize = True
        }

        pnlStatus.Controls.Add(lblStatus)

        Me.Controls.AddRange({pnlMain, pnlStatus, pnlToolbar, pnlHeader})
    End Sub

    Private Sub LoadProducts(Optional searchText As String = "", Optional categoryFilter As String = "")
        Try
            koneksi()

            Dim query As String = "SELECT p.product_id as 'ID', p.product_name as 'Nama Produk', " &
                                 "c.category_name as 'Kategori', p.price as 'Harga', p.stock as 'Stok', " &
                                 "p.description as 'Deskripsi', p.image_url as 'Gambar' " &
                                 "FROM products p LEFT JOIN categories c ON p.category_id = c.category_id WHERE 1=1 "

            If searchText <> "" AndAlso searchText <> "Ketik nama produk..." Then
                query &= "AND p.product_name LIKE '%" & searchText & "%' "
            End If

            If categoryFilter <> "" AndAlso categoryFilter <> "Semua Kategori" Then
                query &= "AND c.category_name = '" & categoryFilter & "' "
            End If

            query &= "ORDER BY p.product_id DESC"

            perintahSQL = New MySqlCommand(query, koneksiDatabase)
            Dim adapter As New MySqlDataAdapter(perintahSQL)
            Dim dt As New DataTable()
            adapter.Fill(dt)

            Dim dgv As DataGridView = CType(Me.Controls.Find("dgvProducts", True).FirstOrDefault(), DataGridView)
            If dgv IsNot Nothing Then
                dgv.DataSource = dt

                ' Format columns
                If dgv.Columns.Contains("Harga") Then
                    dgv.Columns("Harga").DefaultCellStyle.Format = "C0"
                    dgv.Columns("Harga").DefaultCellStyle.FormatProvider = New System.Globalization.CultureInfo("id-ID")
                End If

                If dgv.Columns.Contains("Gambar") Then
                    dgv.Columns("Gambar").Visible = False
                End If

                If dgv.Columns.Contains("ID") Then
                    dgv.Columns("ID").Width = 50
                End If
            End If

            ' Update status
            Dim lblStatus As Label = CType(Me.Controls.Find("lblStatus", True).FirstOrDefault(), Label)
            If lblStatus IsNot Nothing Then
                lblStatus.Text = $"Total: {dt.Rows.Count} produk"
            End If

            tutupKoneksi()

        Catch ex As Exception
            MessageBox.Show("Error loading products: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadCategories()
        Try
            koneksi()

            Dim cmbCategory As ComboBox = CType(Me.Controls.Find("cmbCategory", True).FirstOrDefault(), ComboBox)
            If cmbCategory IsNot Nothing Then
                cmbCategory.Items.Clear()
                cmbCategory.Items.Add("Semua Kategori")

                perintahSQL = New MySqlCommand("SELECT category_name FROM categories ORDER BY category_name", koneksiDatabase)
                Dim reader As MySqlDataReader = perintahSQL.ExecuteReader()

                While reader.Read()
                    cmbCategory.Items.Add(reader("category_name").ToString())
                End While

                reader.Close()
                cmbCategory.SelectedIndex = 0
            End If

            tutupKoneksi()

        Catch ex As Exception
            MessageBox.Show("Error loading categories: " & ex.Message)
        End Try
    End Sub

    Private Sub TxtSearch_TextChanged(sender As Object, e As EventArgs)
        Dim txtBox As TextBox = CType(sender, TextBox)
        Dim searchText As String = txtBox.Text.Trim()

        ' Skip search if placeholder text
        If searchText = "Ketik nama produk..." Then
            searchText = ""
        End If

        Dim cmbCategory As ComboBox = CType(Me.Controls.Find("cmbCategory", True).FirstOrDefault(), ComboBox)
        Dim categoryFilter As String = If(cmbCategory IsNot Nothing, cmbCategory.Text, "")
        LoadProducts(searchText, categoryFilter)
    End Sub

    Private Sub CmbCategory_SelectedIndexChanged(sender As Object, e As EventArgs)
        Dim categoryFilter As String = CType(sender, ComboBox).Text
        Dim txtSearch As TextBox = CType(Me.Controls.Find("txtSearch", True).FirstOrDefault(), TextBox)
        Dim searchText As String = If(txtSearch IsNot Nothing, txtSearch.Text.Trim(), "")
        LoadProducts(searchText, categoryFilter)
    End Sub

    Private Sub DgvProducts_CellDoubleClick(sender As Object, e As DataGridViewCellEventArgs)
        If e.RowIndex >= 0 Then
            BtnEdit_Click(Nothing, Nothing)
        End If
    End Sub

    Private Sub BtnAdd_Click(sender As Object, e As EventArgs)
        Dim formAdd As New FormAddProduct()
        If formAdd.ShowDialog() = DialogResult.OK Then
            LoadProducts()
        End If
    End Sub

    Private Sub BtnEdit_Click(sender As Object, e As EventArgs)
        Dim dgv As DataGridView = CType(Me.Controls.Find("dgvProducts", True).FirstOrDefault(), DataGridView)

        If dgv Is Nothing OrElse dgv.SelectedRows.Count = 0 Then
            MessageBox.Show("Pilih produk yang akan diedit!", "Peringatan", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Dim productID As Integer = Convert.ToInt32(dgv.SelectedRows(0).Cells("ID").Value)
        Dim formEdit As New FormEditProduct(productID)
        If formEdit.ShowDialog() = DialogResult.OK Then
            LoadProducts()
        End If
    End Sub

    Private Sub BtnDelete_Click(sender As Object, e As EventArgs)
        Dim dgv As DataGridView = CType(Me.Controls.Find("dgvProducts", True).FirstOrDefault(), DataGridView)

        If dgv Is Nothing OrElse dgv.SelectedRows.Count = 0 Then
            MessageBox.Show("Pilih produk yang akan dihapus!", "Peringatan", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Dim productID As Integer = Convert.ToInt32(dgv.SelectedRows(0).Cells("ID").Value)
        Dim productName As String = dgv.SelectedRows(0).Cells("Nama Produk").Value.ToString()

        If MessageBox.Show($"Yakin ingin menghapus produk '{productName}'?", "Konfirmasi Hapus", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            Try
                koneksi()
                perintahSQL = New MySqlCommand("DELETE FROM products WHERE product_id = @id", koneksiDatabase)
                perintahSQL.Parameters.AddWithValue("@id", productID)
                perintahSQL.ExecuteNonQuery()
                tutupKoneksi()

                MessageBox.Show("Produk berhasil dihapus!", "Sukses", MessageBoxButtons.OK, MessageBoxIcon.Information)
                LoadProducts()

            Catch ex As Exception
                MessageBox.Show("Error menghapus produk: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub
End Class
