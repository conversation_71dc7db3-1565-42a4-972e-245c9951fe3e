Imports MySql.Data.MySqlClient
Imports System.Drawing

Public Class FormPaymentGateway
    Private orderID As Integer
    Private paymentMethod As String
    Private totalAmount As Decimal
    
    Public Sub New(orderID As Integer, paymentMethod As String, totalAmount As Decimal)
        InitializeComponent()
        Me.orderID = orderID
        Me.paymentMethod = paymentMethod
        Me.totalAmount = totalAmount
    End Sub
    
    Private Sub FormPaymentGateway_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Me.Text = "Payment Gateway - " & paymentMethod
        Me.Size = New Size(600, 500)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.BackColor = Color.FromArgb(245, 245, 245)
        Me.FormBorderStyle = FormBorderStyle.FixedSingle
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        
        CreatePaymentUI()
    End Sub
    
    Private Sub CreatePaymentUI()
        ' Header Panel
        Dim pnlHeader As New Panel With {
            .Location = New Point(20, 20),
            .Size = New Size(540, 80),
            .BackColor = Color.FromArgb(52, 152, 219),
            .BorderStyle = BorderStyle.None
        }
        
        Dim lblTitle As New Label With {
            .Text = "💳 PAYMENT GATEWAY",
            .Font = New Font("Segoe UI", 16, FontStyle.Bold),
            .ForeColor = Color.White,
            .Location = New Point(20, 15),
            .AutoSize = True
        }
        
        Dim lblSubtitle As New Label With {
            .Text = $"Metode: {paymentMethod} | Total: Rp {totalAmount:N0}",
            .Font = New Font("Segoe UI", 11),
            .ForeColor = Color.White,
            .Location = New Point(20, 45),
            .AutoSize = True
        }
        
        pnlHeader.Controls.AddRange({lblTitle, lblSubtitle})
        Me.Controls.Add(pnlHeader)
        
        ' Payment Method Specific UI
        Select Case paymentMethod
            Case "Transfer Bank"
                CreateTransferBankUI()
            Case "COD (Cash on Delivery)"
                CreateCODUI()
            Case "E-Wallet"
                CreateEWalletUI()
            Case "Kartu Kredit"
                CreateCreditCardUI()
        End Select
        
        ' Action Buttons
        CreateActionButtons()
    End Sub
    
    Private Sub CreateTransferBankUI()
        Dim pnlBank As New Panel With {
            .Location = New Point(20, 120),
            .Size = New Size(540, 200),
            .BackColor = Color.White,
            .BorderStyle = BorderStyle.FixedSingle
        }
        
        Dim lblBankInfo As New Label With {
            .Text = "🏦 INFORMASI TRANSFER BANK" & vbCrLf & vbCrLf &
                   "Bank: BCA" & vbCrLf &
                   "No. Rekening: **********" & vbCrLf &
                   "Atas Nama: TOKO ONLINE" & vbCrLf &
                   "Jumlah Transfer: Rp " & totalAmount.ToString("N0") & vbCrLf & vbCrLf &
                   "💡 Klik tombol 'Bayar Sekarang' untuk melanjutkan pembayaran",
            .Font = New Font("Segoe UI", 11),
            .Location = New Point(20, 20),
            .Size = New Size(500, 160),
            .ForeColor = Color.FromArgb(44, 62, 80)
        }
        
        pnlBank.Controls.Add(lblBankInfo)
        Me.Controls.Add(pnlBank)
    End Sub
    
    Private Sub CreateCODUI()
        Dim pnlCOD As New Panel With {
            .Location = New Point(20, 120),
            .Size = New Size(540, 200),
            .BackColor = Color.White,
            .BorderStyle = BorderStyle.FixedSingle
        }
        
        Dim lblCODInfo As New Label With {
            .Text = "💵 CASH ON DELIVERY (COD)" & vbCrLf & vbCrLf &
                   "Pembayaran dilakukan saat barang diterima" & vbCrLf &
                   "Total yang harus dibayar: Rp " & totalAmount.ToString("N0") & vbCrLf & vbCrLf &
                   "✅ Pesanan akan langsung diproses" & vbCrLf &
                   "📦 Siapkan uang pas saat barang tiba" & vbCrLf & vbCrLf &
                   "💡 Klik 'Konfirmasi COD' untuk melanjutkan pesanan",
            .Font = New Font("Segoe UI", 11),
            .Location = New Point(20, 20),
            .Size = New Size(500, 160),
            .ForeColor = Color.FromArgb(44, 62, 80)
        }
        
        pnlCOD.Controls.Add(lblCODInfo)
        Me.Controls.Add(pnlCOD)
    End Sub
    
    Private Sub CreateEWalletUI()
        Dim pnlEWallet As New Panel With {
            .Location = New Point(20, 120),
            .Size = New Size(540, 200),
            .BackColor = Color.White,
            .BorderStyle = BorderStyle.FixedSingle
        }
        
        Dim lblEWalletInfo As New Label With {
            .Text = "📱 E-WALLET PAYMENT" & vbCrLf & vbCrLf &
                   "Pilihan E-Wallet: OVO, GoPay, DANA, LinkAja" & vbCrLf &
                   "Total Pembayaran: Rp " & totalAmount.ToString("N0") & vbCrLf & vbCrLf &
                   "📲 Scan QR Code atau masukkan nomor HP" & vbCrLf &
                   "⚡ Pembayaran instan" & vbCrLf & vbCrLf &
                   "💡 Klik 'Bayar dengan E-Wallet' untuk melanjutkan",
            .Font = New Font("Segoe UI", 11),
            .Location = New Point(20, 20),
            .Size = New Size(500, 160),
            .ForeColor = Color.FromArgb(44, 62, 80)
        }
        
        pnlEWallet.Controls.Add(lblEWalletInfo)
        Me.Controls.Add(pnlEWallet)
    End Sub
    
    Private Sub CreateCreditCardUI()
        Dim pnlCard As New Panel With {
            .Location = New Point(20, 120),
            .Size = New Size(540, 200),
            .BackColor = Color.White,
            .BorderStyle = BorderStyle.FixedSingle
        }
        
        Dim lblCardInfo As New Label With {
            .Text = "💳 KARTU KREDIT" & vbCrLf & vbCrLf &
                   "Kartu yang diterima: Visa, MasterCard, JCB" & vbCrLf &
                   "Total Pembayaran: Rp " & totalAmount.ToString("N0") & vbCrLf & vbCrLf &
                   "🔒 Transaksi aman dengan enkripsi SSL" & vbCrLf &
                   "💰 Cicilan 0% tersedia" & vbCrLf & vbCrLf &
                   "💡 Klik 'Bayar dengan Kartu' untuk melanjutkan pembayaran",
            .Font = New Font("Segoe UI", 11),
            .Location = New Point(20, 20),
            .Size = New Size(500, 160),
            .ForeColor = Color.FromArgb(44, 62, 80)
        }
        
        pnlCard.Controls.Add(lblCardInfo)
        Me.Controls.Add(pnlCard)
    End Sub
    
    Private Sub CreateActionButtons()
        ' Main Payment Button (kiri)
        Dim btnPay As New Button With {
            .Text = GetPaymentButtonText(),
            .Size = New Size(180, 50),
            .Location = New Point(150, 350),
            .BackColor = Color.FromArgb(46, 204, 113),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 11, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnPay.FlatAppearance.BorderSize = 0
        AddHandler btnPay.Click, AddressOf BtnPay_Click

        ' Cancel Button (kanan)
        Dim btnCancel As New Button With {
            .Text = "🚫 Batal",
            .Size = New Size(120, 50),
            .Location = New Point(350, 350),
            .BackColor = Color.Gray,
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 11, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnCancel.FlatAppearance.BorderSize = 0
        AddHandler btnCancel.Click, Sub() Me.Close()

        Me.Controls.AddRange({btnPay, btnCancel})
    End Sub
    
    Private Function GetPaymentButtonText() As String
        Select Case paymentMethod
            Case "Transfer Bank"
                Return "💳 Konfirmasi Transfer"
            Case "COD (Cash on Delivery)"
                Return "✅ Konfirmasi COD"
            Case "E-Wallet"
                Return "📱 Bayar dengan E-Wallet"
            Case "Kartu Kredit"
                Return "💳 Bayar dengan Kartu"
            Case Else
                Return "💳 Proses Pembayaran"
        End Select
    End Function

    Private Sub BtnPay_Click(sender As Object, e As EventArgs)
        ' Konfirmasi pembayaran
        Dim confirmMsg As String = $"Bayar Rp {totalAmount:N0} dengan {paymentMethod}?"

        If MessageBox.Show(confirmMsg, "Konfirmasi",
                          MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            ProcessPayment()
        End If
    End Sub
    
    Private Sub ProcessPayment()
        ' Simulasi proses pembayaran dengan random result untuk demo
        Dim random As New Random()
        Dim success As Boolean = random.Next(1, 101) <= 85 ' 85% chance success

        ' Show loading
        Me.Cursor = Cursors.WaitCursor

        ' Simulate processing time
        System.Threading.Thread.Sleep(2000)

        Me.Cursor = Cursors.Default

        If success Then
            ProcessPaymentSuccess()
        Else
            ProcessPaymentFailure()
        End If
    End Sub

    Private Sub ProcessPaymentSuccess()
        Try
            ' Update status otomatis menggunakan OrderStatusManager
            OrderStatusManager.Instance.UpdateOrderStatusBasedOnUserAction(orderID, "payment_success")

            ' Get estimasi waktu status berikutnya
            Dim nextStatusInfo As String = OrderStatusManager.Instance.GetEstimatedNextStatusTime(orderID)

            ' Success message dengan status langsung berhasil
            Dim successMsg As String = $"✅ Pembayaran berhasil!" & vbCrLf &
                                      $"Order #{orderID} telah berhasil dibayar!" & vbCrLf &
                                      $"🎉 Pesanan Anda telah selesai diproses!" & vbCrLf &
                                      $"Terima kasih atas pembelian Anda."

            MessageBox.Show(successMsg, "Berhasil", MessageBoxButtons.OK, MessageBoxIcon.Information)

            Me.DialogResult = DialogResult.OK
            Me.Close()

        Catch ex As Exception
            MessageBox.Show("Error updating payment status: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ProcessPaymentFailure()
        ' Update status menggunakan OrderStatusManager (tetap pending)
        OrderStatusManager.Instance.UpdateOrderStatusBasedOnUserAction(orderID, "payment_failed")

        Dim failMsg As String = $"❌ Pembayaran gagal!" & vbCrLf &
                               $"Order #{orderID} tetap berstatus: Menunggu Pembayaran" & vbCrLf & vbCrLf &
                               "💡 Anda masih bisa mencoba lagi:" & vbCrLf &
                               "• Klik tombol pembayaran lagi" & vbCrLf &
                               "• Atau buka menu 'Pesanan Saya' untuk melanjutkan pembayaran" & vbCrLf & vbCrLf &
                               "⚠️ Pesanan akan dibatalkan otomatis dalam 10 menit jika tidak dibayar."

        Dim result As DialogResult = MessageBox.Show(failMsg & vbCrLf & vbCrLf & "Coba bayar lagi sekarang?",
                                                    "Pembayaran Gagal",
                                                    MessageBoxButtons.YesNo,
                                                    MessageBoxIcon.Warning)

        If result = DialogResult.Yes Then
            ' User ingin mencoba lagi - tetap di form pembayaran
            Return
        Else
            ' User tidak ingin mencoba lagi - tutup form
            Me.DialogResult = DialogResult.Cancel
            Me.Close()
        End If
    End Sub

    Private Function GetStatusIndonesian(status As String) As String
        Select Case status.ToLower()
            Case "pending"
                Return "Menunggu Pembayaran"
            Case "success"
                Return "Berhasil Selesai"
            Case "cancelled"
                Return "Dibatalkan"
            Case Else
                Return status
        End Select
    End Function
End Class
