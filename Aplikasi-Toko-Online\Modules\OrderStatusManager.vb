Imports MySql.Data.MySqlClient
Imports System.Threading

Public Class OrderStatusManager
    Private Shared _instance As OrderStatusManager
    Private timer As Timer
    
    Public Shared ReadOnly Property Instance() As OrderStatusManager
        Get
            If _instance Is Nothing Then
                _instance = New OrderStatusManager()
            End If
            Return _instance
        End Get
    End Property
    
    Private Sub New()
        ' Timer untuk auto-update status setiap 30 detik
        timer = New Timer(AddressOf AutoUpdateOrderStatus, Nothing, TimeSpan.Zero, TimeSpan.FromSeconds(30))
    End Sub
    
    ' Method untuk auto-update status pesanan berdasarkan kondisi
    Private Sub AutoUpdateOrderStatus(state As Object)
        Try
            koneksi()

            ' Auto-cancel pending orders setelah 24 jam (untuk demo: 10 menit)
            Dim queryAutoCancelPending As String =
                "UPDATE orders SET order_status = 'cancelled', updated_at = NOW() " &
                "WHERE order_status = 'pending' " &
                "AND TIMESTAMPDIFF(MINUTE, order_date, NOW()) >= 10"

            perintahSQL = New MySqlCommand(queryAutoCancelPending, koneksiDatabase)
            Dim autoCancelled As Integer = perintahSQL.ExecuteNonQuery()

            tutupKoneksi()

        Catch ex As Exception
            ' Error in auto-update
        End Try
    End Sub
    
    ' Method untuk manual update status berdasarkan kondisi user
    Public Sub UpdateOrderStatusBasedOnUserAction(orderID As Integer, userAction As String)
        Try
            koneksi()

            Dim newStatus As String = ""

            Select Case userAction.ToLower()
                Case "payment_success"
                    newStatus = "success"
                Case "payment_failed"
                    ' Status tetap pending
                    Return
                Case "user_cancel"
                    newStatus = "cancelled"
                Case "confirm_delivery"
                    newStatus = "success"
            End Select

            If newStatus <> "" Then
                ' Get current status for verification
                Dim checkCmd As New MySqlCommand("SELECT order_status FROM orders WHERE order_id = @id", koneksiDatabase)
                checkCmd.Parameters.AddWithValue("@id", orderID)
                Dim currentStatus As Object = checkCmd.ExecuteScalar()

                ' Check if updated_at column exists
                Dim checkColumnCmd As New MySqlCommand("SHOW COLUMNS FROM orders LIKE 'updated_at'", koneksiDatabase)
                Dim columnExists As Boolean = False

                Dim columnReader As MySqlDataReader = checkColumnCmd.ExecuteReader()
                If columnReader.Read() Then
                    columnExists = True
                End If
                columnReader.Close()

                ' Update order status with or without updated_at
                Dim updateQuery As String
                If columnExists Then
                    updateQuery = "UPDATE orders SET order_status = @status, updated_at = NOW() WHERE order_id = @id"
                Else
                    updateQuery = "UPDATE orders SET order_status = @status WHERE order_id = @id"
                End If

                perintahSQL = New MySqlCommand(updateQuery, koneksiDatabase)
                perintahSQL.Parameters.AddWithValue("@status", newStatus)
                perintahSQL.Parameters.AddWithValue("@id", orderID)
                Dim rowsAffected As Integer = perintahSQL.ExecuteNonQuery()

                ' Verify update
                Dim verifyCmd As New MySqlCommand("SELECT order_status FROM orders WHERE order_id = @id", koneksiDatabase)
                verifyCmd.Parameters.AddWithValue("@id", orderID)
                Dim updatedStatus As Object = verifyCmd.ExecuteScalar()

                ' Show success message
                MessageBox.Show($"Status order #{orderID} berhasil diubah dari '{currentStatus}' ke '{newStatus}'",
                               "Update Berhasil", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If

            tutupKoneksi()

        Catch ex As Exception
            MessageBox.Show($"Error updating order status: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            tutupKoneksi()
        End Try
    End Sub
    
    ' Method untuk cek apakah pesanan bisa dibayar
    Public Function CanOrderBePaid(orderID As Integer) As Boolean
        Try
            koneksi()
            
            perintahSQL = New MySqlCommand("SELECT order_status FROM orders WHERE order_id = @id", koneksiDatabase)
            perintahSQL.Parameters.AddWithValue("@id", orderID)

            Dim status As Object = perintahSQL.ExecuteScalar()
            tutupKoneksi()
            
            If status IsNot Nothing Then
                Return status.ToString().ToLower() = "pending"
            End If
            
            Return False
            
        Catch ex As Exception
            Return False
        End Try
    End Function
    
    ' Method untuk mendapatkan status pesanan
    Public Function GetOrderStatus(orderID As Integer) As String
        Try
            koneksi()
            
            perintahSQL = New MySqlCommand("SELECT order_status FROM orders WHERE order_id = @id", koneksiDatabase)
            perintahSQL.Parameters.AddWithValue("@id", orderID)

            Dim status As Object = perintahSQL.ExecuteScalar()
            tutupKoneksi()
            
            If status IsNot Nothing Then
                Return status.ToString()
            End If
            
            Return "unknown"
            
        Catch ex As Exception
            Return "error"
        End Try
    End Function
    

    
    ' Method untuk mendapatkan estimasi waktu status berikutnya
    Public Function GetEstimatedNextStatusTime(orderID As Integer) As String
        Try
            koneksi()
            
            perintahSQL = New MySqlCommand("SELECT order_status, updated_at FROM orders WHERE order_id = @id", koneksiDatabase)
            perintahSQL.Parameters.AddWithValue("@id", orderID)

            Dim reader As MySqlDataReader = perintahSQL.ExecuteReader()
            
            If reader.Read() Then
                Dim status As String = reader("order_status").ToString()
                Dim updatedAt As DateTime = Convert.ToDateTime(reader("updated_at"))
                reader.Close()
                tutupKoneksi()
                
                Select Case status.ToLower()
                    Case "pending"
                        Return "Menunggu pembayaran"
                    Case "success"
                        Return "Pesanan selesai"
                    Case "cancelled"
                        Return "Pesanan dibatalkan"
                    Case Else
                        Return "Status tidak diketahui"
                End Select
            End If
            
            reader.Close()
            tutupKoneksi()
            Return "Tidak dapat memperkirakan"
            
        Catch ex As Exception
            Return "Error mendapatkan estimasi"
        End Try
    End Function
    
    ' Method untuk cleanup resources
    Public Sub Dispose()
        If timer IsNot Nothing Then
            timer.Dispose()
            timer = Nothing
        End If
    End Sub
End Class
