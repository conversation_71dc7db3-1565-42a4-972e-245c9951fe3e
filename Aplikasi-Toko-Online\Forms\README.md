# Forms Directory Structure

Folder ini berisi semua form/tampilan aplikasi yang diorganisir berdasarkan peran pengguna.

## 📁 Struktur Folder

### 🔐 Auth/

Berisi form-form terkait autentikasi dan registrasi:

- `FormLogin.*` - Form login pengguna
- `FormRegister.*` - Form registrasi pengguna baru

### 👤 User/

Berisi form-form untuk pengguna biasa:

- `FormMain.*` - Form utama aplikasi (dashboard user)
- `FormCart.*` - Form keranjang belanja
- `FormCheckout.*` - Form checkout/pembayaran
- `FormMyOrders.*` - Form daftar pesanan user
- `FormOrderDetail.*` - Form detail pesanan
- `FormPaymentGateway.*` - Form gateway pembayaran
- `FormWishlist.*` - Form wishlist/daftar keinginan

### 👨‍💼 Admin/

Berisi form-form untuk administrator:

- `FormAdminDashboard.*` - Dashboard admin
- `FormAdminOrders.*` - <PERSON><PERSON><PERSON> pesanan
- `FormAdminProducts.*` - <PERSON><PERSON>la produk
- `FormAdminUsers.*` - <PERSON><PERSON>la pengguna
- `FormAddProduct.*` - Tambah produk baru
- `FormEditProduct.*` - Edit produk
- `FormDeleteProduct.*` - Hapus produk

## 🎯 Keuntungan Struktur Ini

1. **Organisasi yang Jelas** - Mudah menemukan form berdasarkan fungsi
2. **Pemeliharaan Mudah** - Perubahan pada satu modul tidak mempengaruhi yang lain
3. **Skalabilitas** - Mudah menambah form baru ke kategori yang tepat
4. **Tim Development** - Developer bisa fokus pada area tertentu
5. **Security** - Pemisahan yang jelas antara form admin dan user

## 📝 Konvensi Penamaan

- Semua form menggunakan prefix `Form`
- File Designer: `.Designer.vb`
- File Resource: `.resx`
- File Code-behind: `.vb`
